******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 00:01:08 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003ebd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005368  0001ac98  R  X
  SRAM                  20200000   00008000  00000416  00007bea  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005368   00005368    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004850   00004850    r-x .text
  00004910    00004910    00000a00   00000a00    r-- .rodata
  00005310    00005310    00000058   00000058    r-- .cinit
20200000    20200000    00000219   00000000    rw-
  20200000    20200000    000001b5   00000000    rw- .bss
  202001b8    202001b8    00000061   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004850     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000280     Task_App.o (.text.Task_OLED)
                  00000d10    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000f30    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000110c    000001d8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000012e4    000001b0     Task.o (.text.Task_Start)
                  00001494    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001626    00000002     Task_App.o (.text.Task_IdleFunction)
                  00001628    0000014c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001774    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000018b0    00000134            : qsort.c.obj (.text.qsort)
                  000019e4    00000130     OLED.o (.text.OLED_ShowChar)
                  00001b14    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001c34    00000110     OLED.o (.text.OLED_Init)
                  00001d44    0000010c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001e50    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001f5c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002060    000000f0     Motor.o (.text.Motor_SetDirc)
                  00002150    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002234    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002310    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000023e8    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  0000249c    000000b4     Task.o (.text.Task_Add)
                  00002550    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000025fa    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000025fc    000000a8     Motor.o (.text.Motor_SetSpeed)
                  000026a4    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002746    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002748    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000027e8    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002880    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  0000290c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00002990    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002a14    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002a96    00000002     --HOLE-- [fill = 0]
                  00002a98    00000080     Task_App.o (.text.Task_GraySensor)
                  00002b18    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002b94    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002c08    00000070     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002c78    0000006e     OLED.o (.text.OLED_ShowString)
                  00002ce6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002d50    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002db8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002e1e    00000002     --HOLE-- [fill = 0]
                  00002e20    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002e84    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002ee8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002f4a    00000002     --HOLE-- [fill = 0]
                  00002f4c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002fae    00000002     --HOLE-- [fill = 0]
                  00002fb0    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003010    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000306e    00000002     --HOLE-- [fill = 0]
                  00003070    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000030cc    0000005c     Task_App.o (.text.Task_Init)
                  00003128    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003184    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000031e0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00003238    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003290    00000058            : _printfi.c.obj (.text._pconv_f)
                  000032e8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000333e    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003390    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000033e0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003430    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  0000347c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000034c8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00003514    0000004c     OLED.o (.text.OLED_Printf)
                  00003560    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000035ac    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000035f6    00000002     --HOLE-- [fill = 0]
                  000035f8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003642    00000002     --HOLE-- [fill = 0]
                  00003644    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000368c    00000048     ADC.o (.text.adc_getValue)
                  000036d4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003718    00000044     OLED.o (.text.mspm0_i2c_disable)
                  0000375c    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000379e    00000002     --HOLE-- [fill = 0]
                  000037a0    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000037e2    00000002     --HOLE-- [fill = 0]
                  000037e4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003824    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003864    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000038a4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000038e4    0000003e     Task.o (.text.Task_CMP)
                  00003922    00000002     --HOLE-- [fill = 0]
                  00003924    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003960    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000399c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000039d8    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003a14    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003a50    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003a8c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003ac8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003b04    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003b3e    00000002     --HOLE-- [fill = 0]
                  00003b40    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003b7a    00000002     --HOLE-- [fill = 0]
                  00003b7c    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003bb0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003be4    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00003c14    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00003c44    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003c74    0000002c     Interrupt.o (.text.Interrupt_Init)
                  00003ca0    0000002c     Motor.o (.text.Motor_Start)
                  00003ccc    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003cf8    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003d24    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003d50    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003d7c    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003da4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003dcc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003df4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003e1c    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003e44    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00003e6c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003e94    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003ebc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003ee4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003f0a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003f30    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003f54    00000024     Motor.o (.text.Motor_SetBothSpeed)
                  00003f78    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003f9c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003fc0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003fe2    00000002     --HOLE-- [fill = 0]
                  00003fe4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004004    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004024    00000020     SysTick.o (.text.Delay)
                  00004044    00000020     main.o (.text.main)
                  00004064    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004082    00000002     --HOLE-- [fill = 0]
                  00004084    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000040a2    00000002     --HOLE-- [fill = 0]
                  000040a4    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000040c0    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  000040dc    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000040f8    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00004114    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004130    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  0000414c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004168    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004184    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000041a0    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000041bc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000041d8    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000041f4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004210    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000422c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004248    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004264    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004280    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00004298    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000042b0    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000042c8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000042e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000042f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004310    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00004328    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004340    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004358    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004370    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004388    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000043a0    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000043b8    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000043d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000043e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004400    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004418    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004430    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004448    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004460    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004478    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004490    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000044a8    00000018     OLED.o (.text.DL_I2C_reset)
                  000044c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000044d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000044f0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004508    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004520    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004538    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004550    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004568    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004580    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004598    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000045b0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000045c8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000045e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000045f8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004610    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00004628    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004640    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00004656    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  0000466c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004682    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004698    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000046ae    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000046c4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000046da    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000046ee    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00004702    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00004716    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000472a    00000002     --HOLE-- [fill = 0]
                  0000472c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004740    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004754    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004768    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000477c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004790    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000047a4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000047b8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000047ca    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000047dc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000047ee    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000047fe    00000002     --HOLE-- [fill = 0]
                  00004800    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004810    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004820    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004830    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004840    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000484e    00000002     --HOLE-- [fill = 0]
                  00004850    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000485e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000486c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000487a    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004886    00000002     --HOLE-- [fill = 0]
                  00004888    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004894    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000489e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000048a8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000048b8    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000048c2    0000000a            : vsprintf.c.obj (.text._outc)
                  000048cc    00000008     Interrupt.o (.text.SysTick_Handler)
                  000048d4    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000048dc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000048e4    00000006     libc.a : exit.c.obj (.text:abort)
                  000048ea    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000048ee    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000048f2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000048f6    00000002     --HOLE-- [fill = 0]
                  000048f8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004908    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000490c    00000004     --HOLE-- [fill = 0]

.cinit     0    00005310    00000058     
                  00005310    0000002d     (.cinit..data.load) [load image, compression = lzss]
                  0000533d    00000003     --HOLE-- [fill = 0]
                  00005340    0000000c     (__TI_handler_table)
                  0000534c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005354    00000010     (__TI_cinit_table)
                  00005364    00000004     --HOLE-- [fill = 0]

.rodata    0    00004910    00000a00     
                  00004910    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004f00    0000021c     OLED_Font.o (.rodata.asc2_0806)
                  0000511c    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000511f    00000001     --HOLE-- [fill = 0]
                  00005120    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005221    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005223    00000001     --HOLE-- [fill = 0]
                  00005224    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000524c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005264    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000527c    00000016     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00005292    00000013     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000052a5    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000052b6    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000052c7    0000000b     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000052d2    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000052dd    0000000b     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000052e8    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000052f2    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000052f4    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  000052fc    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00005304    00000005     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005309    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000530b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b5     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    000000b0     (.common:GraySensor)
                  202001a0    00000010     (.common:Gray_Anolog)
                  202001b0    00000004     (.common:ExISR_Flag)
                  202001b4    00000001     (.common:Gray_Digtal)

.data      0    202001b8    00000061     UNINITIALIZED
                  202001b8    00000020     Motor.o (.data.Motor_Left)
                  202001d8    00000020     Motor.o (.data.Motor_Right)
                  202001f8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202001fc    00000004     Task_App.o (.data.Task_GraySensor.debug_counter)
                  20200200    00000004     Task_App.o (.data.Task_OLED.display_counter)
                  20200204    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200208    00000004     SysTick.o (.data.delayTick)
                  2020020c    00000004     Task_App.o (.data.last_time)
                  20200210    00000004     SysTick.o (.data.uwTick)
                  20200214    00000002     Task_App.o (.data.last_encoder_left)
                  20200216    00000002     Task_App.o (.data.last_encoder_right)
                  20200218    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3418    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3458    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       862     79        213    
       Interrupt.o                      378     0         4      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1240    79        217    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2060      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1066    0         0      
       Task.o                           674     0         241    
       Motor.o                          556     0         64     
       ADC.o                            236     0         0      
       SysTick.o                        84      0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4470    2060      313    
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1116    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5736    291       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       udivmoddi4.S.obj                 162     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2444    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       81        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     18468   2826      1046   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005354 records: 2, size/record: 8, table size: 16
	.data: load addr=00005310, load size=0000002d bytes, run addr=202001b8, run size=00000061 bytes, compression=lzss
	.bss: load addr=0000534c, load size=00000008 bytes, run addr=20200000, run size=000001b5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005340 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001495     000048a8     000048a6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003ebd     000048f8     000048f2   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000048eb  ADC0_IRQHandler                      
000048eb  ADC1_IRQHandler                      
000048eb  AES_IRQHandler                       
000048ee  C$$EXIT                              
000048eb  CANFD0_IRQHandler                    
000048eb  DAC0_IRQHandler                      
000037e5  DL_ADC12_setClockConfig              
00004895  DL_Common_delayCycles                
0000347d  DL_DMA_initChannel                   
00003011  DL_I2C_fillControllerTXFIFO          
00003f0b  DL_I2C_setClockConfig                
00002235  DL_SYSCTL_configSYSPLL               
00002e21  DL_SYSCTL_setHFCLKSourceHFXTParams   
000036d5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001f5d  DL_Timer_initFourCCPWMMode           
0000422d  DL_Timer_setCaptCompUpdateMethod     
00004569  DL_Timer_setCaptureCompareOutCtl     
00004811  DL_Timer_setCaptureCompareValue      
00004249  DL_Timer_setClockConfig              
00003645  DL_UART_init                         
000047b9  DL_UART_setClockConfig               
000048eb  DMA_IRQHandler                       
202001f8  Data_MotorEncoder                    
000048eb  Default_Handler                      
00004025  Delay                                
202001b0  ExISR_Flag                           
000048eb  GROUP0_IRQHandler                    
000023e9  GROUP1_IRQHandler                    
00001d45  Get_Analog_value                     
000039d9  Get_Anolog_Value                     
00004841  Get_Digtal_For_User                  
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b4  Gray_Digtal                          
000048ef  HOSTexit                             
000048eb  HardFault_Handler                    
000048eb  I2C0_IRQHandler                      
000048eb  I2C1_IRQHandler                      
00002ce7  I2C_OLED_Clear                       
00003a15  I2C_OLED_Set_Pos                     
000027e9  I2C_OLED_WR_Byte                     
00002fb1  I2C_OLED_i2c_sda_unlock              
00003c75  Interrupt_Init                       
202001b8  Motor_Left                           
202001d8  Motor_Right                          
00003f55  Motor_SetBothSpeed                   
000025fd  Motor_SetSpeed                       
00003ca1  Motor_Start                          
000048eb  NMI_Handler                          
00001629  No_MCU_Ganv_Sensor_Init_Frist        
0000375d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001c35  OLED_Init                            
00003515  OLED_Printf                          
000019e5  OLED_ShowChar                        
00002c79  OLED_ShowString                      
000048eb  PendSV_Handler                       
000048eb  RTC_IRQHandler                       
000048f3  Reset_Handler                        
000048eb  SPI0_IRQHandler                      
000048eb  SPI1_IRQHandler                      
000048eb  SVC_Handler                          
00003561  SYSCFG_DL_ADC1_init                  
00003c15  SYSCFG_DL_DMA_CH_RX_init             
00004611  SYSCFG_DL_DMA_CH_TX_init             
0000487b  SYSCFG_DL_DMA_init                   
0000110d  SYSCFG_DL_GPIO_init                  
000031e1  SYSCFG_DL_I2C_MPU6050_init           
00002e85  SYSCFG_DL_I2C_OLED_init              
00002881  SYSCFG_DL_Motor_PWM_init             
00003071  SYSCFG_DL_SYSCTL_init                
00004821  SYSCFG_DL_SYSTICK_init               
0000290d  SYSCFG_DL_UART0_init                 
00003ccd  SYSCFG_DL_init                       
00002749  SYSCFG_DL_initPower                  
000048cd  SysTick_Handler                      
00003e6d  SysTick_Increasment                  
00004889  Sys_GetTick                          
000048eb  TIMA0_IRQHandler                     
000048eb  TIMA1_IRQHandler                     
000048eb  TIMG0_IRQHandler                     
000048eb  TIMG12_IRQHandler                    
000048eb  TIMG6_IRQHandler                     
000048eb  TIMG7_IRQHandler                     
000048eb  TIMG8_IRQHandler                     
000047cb  TI_memcpy_small                      
0000486d  TI_memset_small                      
0000249d  Task_Add                             
00002a99  Task_GraySensor                      
00001627  Task_IdleFunction                    
000030cd  Task_Init                            
00000a91  Task_OLED                            
000012e5  Task_Start                           
000048eb  UART0_IRQHandler                     
000048eb  UART1_IRQHandler                     
000048eb  UART2_IRQHandler                     
000048eb  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005354  __TI_CINIT_Base                      
00005364  __TI_CINIT_Limit                     
00005364  __TI_CINIT_Warm                      
00005340  __TI_Handler_Table_Base              
0000534c  __TI_Handler_Table_Limit             
00003ac9  __TI_auto_init_nobinit_nopinit       
00002b19  __TI_decompress_lzss                 
000047dd  __TI_decompress_none                 
00003239  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000046c5  __TI_zero_init_nomemset              
0000149f  __adddf3                             
00005120  __aeabi_ctype_table_                 
00005120  __aeabi_ctype_table_C                
000035f9  __aeabi_d2iz                         
000037a1  __aeabi_d2uiz                        
0000149f  __aeabi_dadd                         
00002ee9  __aeabi_dcmpeq                       
00002f25  __aeabi_dcmpge                       
00002f39  __aeabi_dcmpgt                       
00002f11  __aeabi_dcmple                       
00002efd  __aeabi_dcmplt                       
00001e51  __aeabi_ddiv                         
00002151  __aeabi_dmul                         
00001495  __aeabi_dsub                         
20200204  __aeabi_errno                        
000048d5  __aeabi_errno_addr                   
00003865  __aeabi_f2d                          
00002f4d  __aeabi_fcmpeq                       
00002f89  __aeabi_fcmpge                       
00002f9d  __aeabi_fcmpgt                       
00002f75  __aeabi_fcmple                       
00002f61  __aeabi_fcmplt                       
00002a15  __aeabi_fdiv                         
00003d25  __aeabi_i2d                          
00003a51  __aeabi_i2f                          
000032e9  __aeabi_idiv                         
000025fb  __aeabi_idiv0                        
000032e9  __aeabi_idivmod                      
00002747  __aeabi_ldiv0                        
00004085  __aeabi_llsl                         
00003f9d  __aeabi_lmul                         
000048dd  __aeabi_memcpy                       
000048dd  __aeabi_memcpy4                      
000048dd  __aeabi_memcpy8                      
00004851  __aeabi_memset                       
00004851  __aeabi_memset4                      
00004851  __aeabi_memset8                      
00003f79  __aeabi_ui2d                         
00003e95  __aeabi_ui2f                         
00003825  __aeabi_uidiv                        
00003825  __aeabi_uidivmod                     
00004791  __aeabi_uldivmod                     
00004085  __ashldi3                            
ffffffff  __binit__                            
00002d51  __cmpdf2                             
00003b05  __cmpsf2                             
00001e51  __divdf3                             
00002a15  __divsf3                             
00002d51  __eqdf2                              
00003b05  __eqsf2                              
00003865  __extendsfdf2                        
000035f9  __fixdfsi                            
000037a1  __fixunsdfsi                         
00003d25  __floatsidf                          
00003a51  __floatsisf                          
00003f79  __floatunsidf                        
00003e95  __floatunsisf                        
00002b95  __gedf2                              
00003a8d  __gesf2                              
00002b95  __gtdf2                              
00003a8d  __gtsf2                              
00002d51  __ledf2                              
00003b05  __lesf2                              
00002d51  __ltdf2                              
00003b05  __ltsf2                              
UNDEFED   __mpu_init                           
00002151  __muldf3                             
00003f9d  __muldi3                             
00003b41  __muldsi3                            
00002d51  __nedf2                              
00003b05  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001495  __subdf3                             
000026a5  __udivmoddi4                         
00003ebd  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00004909  _system_pre_init                     
000048e5  abort                                
0000368d  adc_getValue                         
00004f00  asc2_0806                            
00004910  asc2_1608                            
000038a5  atoi                                 
ffffffff  binit                                
00002c09  convertAnalogToDigital               
20200208  delayTick                            
00003129  frexp                                
00003129  frexpl                               
00000000  interruptVectors                     
00002311  ldexp                                
00002311  ldexpl                               
00004045  main                                 
00003fc1  memccpy                              
00002551  normalizeAnalogValues                
000018b1  qsort                                
00002311  scalbn                               
00002311  scalbnl                              
20200210  uwTick                               
00003d51  vsprintf                             
00004831  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  Task_OLED                            
0000110d  SYSCFG_DL_GPIO_init                  
000012e5  Task_Start                           
00001495  __aeabi_dsub                         
00001495  __subdf3                             
0000149f  __adddf3                             
0000149f  __aeabi_dadd                         
00001627  Task_IdleFunction                    
00001629  No_MCU_Ganv_Sensor_Init_Frist        
000018b1  qsort                                
000019e5  OLED_ShowChar                        
00001c35  OLED_Init                            
00001d45  Get_Analog_value                     
00001e51  __aeabi_ddiv                         
00001e51  __divdf3                             
00001f5d  DL_Timer_initFourCCPWMMode           
00002151  __aeabi_dmul                         
00002151  __muldf3                             
00002235  DL_SYSCTL_configSYSPLL               
00002311  ldexp                                
00002311  ldexpl                               
00002311  scalbn                               
00002311  scalbnl                              
000023e9  GROUP1_IRQHandler                    
0000249d  Task_Add                             
00002551  normalizeAnalogValues                
000025fb  __aeabi_idiv0                        
000025fd  Motor_SetSpeed                       
000026a5  __udivmoddi4                         
00002747  __aeabi_ldiv0                        
00002749  SYSCFG_DL_initPower                  
000027e9  I2C_OLED_WR_Byte                     
00002881  SYSCFG_DL_Motor_PWM_init             
0000290d  SYSCFG_DL_UART0_init                 
00002a15  __aeabi_fdiv                         
00002a15  __divsf3                             
00002a99  Task_GraySensor                      
00002b19  __TI_decompress_lzss                 
00002b95  __gedf2                              
00002b95  __gtdf2                              
00002c09  convertAnalogToDigital               
00002c79  OLED_ShowString                      
00002ce7  I2C_OLED_Clear                       
00002d51  __cmpdf2                             
00002d51  __eqdf2                              
00002d51  __ledf2                              
00002d51  __ltdf2                              
00002d51  __nedf2                              
00002e21  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002e85  SYSCFG_DL_I2C_OLED_init              
00002ee9  __aeabi_dcmpeq                       
00002efd  __aeabi_dcmplt                       
00002f11  __aeabi_dcmple                       
00002f25  __aeabi_dcmpge                       
00002f39  __aeabi_dcmpgt                       
00002f4d  __aeabi_fcmpeq                       
00002f61  __aeabi_fcmplt                       
00002f75  __aeabi_fcmple                       
00002f89  __aeabi_fcmpge                       
00002f9d  __aeabi_fcmpgt                       
00002fb1  I2C_OLED_i2c_sda_unlock              
00003011  DL_I2C_fillControllerTXFIFO          
00003071  SYSCFG_DL_SYSCTL_init                
000030cd  Task_Init                            
00003129  frexp                                
00003129  frexpl                               
000031e1  SYSCFG_DL_I2C_MPU6050_init           
00003239  __TI_ltoa                            
000032e9  __aeabi_idiv                         
000032e9  __aeabi_idivmod                      
0000347d  DL_DMA_initChannel                   
00003515  OLED_Printf                          
00003561  SYSCFG_DL_ADC1_init                  
000035f9  __aeabi_d2iz                         
000035f9  __fixdfsi                            
00003645  DL_UART_init                         
0000368d  adc_getValue                         
000036d5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000375d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000037a1  __aeabi_d2uiz                        
000037a1  __fixunsdfsi                         
000037e5  DL_ADC12_setClockConfig              
00003825  __aeabi_uidiv                        
00003825  __aeabi_uidivmod                     
00003865  __aeabi_f2d                          
00003865  __extendsfdf2                        
000038a5  atoi                                 
000039d9  Get_Anolog_Value                     
00003a15  I2C_OLED_Set_Pos                     
00003a51  __aeabi_i2f                          
00003a51  __floatsisf                          
00003a8d  __gesf2                              
00003a8d  __gtsf2                              
00003ac9  __TI_auto_init_nobinit_nopinit       
00003b05  __cmpsf2                             
00003b05  __eqsf2                              
00003b05  __lesf2                              
00003b05  __ltsf2                              
00003b05  __nesf2                              
00003b41  __muldsi3                            
00003c15  SYSCFG_DL_DMA_CH_RX_init             
00003c75  Interrupt_Init                       
00003ca1  Motor_Start                          
00003ccd  SYSCFG_DL_init                       
00003d25  __aeabi_i2d                          
00003d25  __floatsidf                          
00003d51  vsprintf                             
00003e6d  SysTick_Increasment                  
00003e95  __aeabi_ui2f                         
00003e95  __floatunsisf                        
00003ebd  _c_int00_noargs                      
00003f0b  DL_I2C_setClockConfig                
00003f55  Motor_SetBothSpeed                   
00003f79  __aeabi_ui2d                         
00003f79  __floatunsidf                        
00003f9d  __aeabi_lmul                         
00003f9d  __muldi3                             
00003fc1  memccpy                              
00004025  Delay                                
00004045  main                                 
00004085  __aeabi_llsl                         
00004085  __ashldi3                            
0000422d  DL_Timer_setCaptCompUpdateMethod     
00004249  DL_Timer_setClockConfig              
00004569  DL_Timer_setCaptureCompareOutCtl     
00004611  SYSCFG_DL_DMA_CH_TX_init             
000046c5  __TI_zero_init_nomemset              
00004791  __aeabi_uldivmod                     
000047b9  DL_UART_setClockConfig               
000047cb  TI_memcpy_small                      
000047dd  __TI_decompress_none                 
00004811  DL_Timer_setCaptureCompareValue      
00004821  SYSCFG_DL_SYSTICK_init               
00004831  wcslen                               
00004841  Get_Digtal_For_User                  
00004851  __aeabi_memset                       
00004851  __aeabi_memset4                      
00004851  __aeabi_memset8                      
0000486d  TI_memset_small                      
0000487b  SYSCFG_DL_DMA_init                   
00004889  Sys_GetTick                          
00004895  DL_Common_delayCycles                
000048cd  SysTick_Handler                      
000048d5  __aeabi_errno_addr                   
000048dd  __aeabi_memcpy                       
000048dd  __aeabi_memcpy4                      
000048dd  __aeabi_memcpy8                      
000048e5  abort                                
000048eb  ADC0_IRQHandler                      
000048eb  ADC1_IRQHandler                      
000048eb  AES_IRQHandler                       
000048eb  CANFD0_IRQHandler                    
000048eb  DAC0_IRQHandler                      
000048eb  DMA_IRQHandler                       
000048eb  Default_Handler                      
000048eb  GROUP0_IRQHandler                    
000048eb  HardFault_Handler                    
000048eb  I2C0_IRQHandler                      
000048eb  I2C1_IRQHandler                      
000048eb  NMI_Handler                          
000048eb  PendSV_Handler                       
000048eb  RTC_IRQHandler                       
000048eb  SPI0_IRQHandler                      
000048eb  SPI1_IRQHandler                      
000048eb  SVC_Handler                          
000048eb  TIMA0_IRQHandler                     
000048eb  TIMA1_IRQHandler                     
000048eb  TIMG0_IRQHandler                     
000048eb  TIMG12_IRQHandler                    
000048eb  TIMG6_IRQHandler                     
000048eb  TIMG7_IRQHandler                     
000048eb  TIMG8_IRQHandler                     
000048eb  UART0_IRQHandler                     
000048eb  UART1_IRQHandler                     
000048eb  UART2_IRQHandler                     
000048eb  UART3_IRQHandler                     
000048ee  C$$EXIT                              
000048ef  HOSTexit                             
000048f3  Reset_Handler                        
00004909  _system_pre_init                     
00004910  asc2_1608                            
00004f00  asc2_0806                            
00005120  __aeabi_ctype_table_                 
00005120  __aeabi_ctype_table_C                
00005340  __TI_Handler_Table_Base              
0000534c  __TI_Handler_Table_Limit             
00005354  __TI_CINIT_Base                      
00005364  __TI_CINIT_Limit                     
00005364  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b0  ExISR_Flag                           
202001b4  Gray_Digtal                          
202001b8  Motor_Left                           
202001d8  Motor_Right                          
202001f8  Data_MotorEncoder                    
20200204  __aeabi_errno                        
20200208  delayTick                            
20200210  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[225 symbols]

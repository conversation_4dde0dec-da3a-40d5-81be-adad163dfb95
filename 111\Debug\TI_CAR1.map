******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 00:14:04 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003f65


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005450  0001abb0  R  X
  SRAM                  20200000   00008000  00000417  00007be9  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000049f8   000049f8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004938   00004938    r-x .text
00004a00    00004a00    00000a58   00000a58    r--
  00004a00    00004a00    00000a00   00000a00    r-- .rodata
  00005400    00005400    00000058   00000058    r-- .cinit
20200000    20200000    0000021a   00000000    rw-
  20200000    20200000    000001b5   00000000    rw- .bss
  202001b8    202001b8    00000062   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004938     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000290     Task_App.o (.text.Task_OLED)
                  00000d20    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00000f40    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000111c    000001d8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000012f4    000001b0     Task.o (.text.Task_Start)
                  000014a4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001636    00000002     Task_App.o (.text.Task_IdleFunction)
                  00001638    0000014c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001784    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000018c0    00000134            : qsort.c.obj (.text.qsort)
                  000019f4    00000130     OLED.o (.text.OLED_ShowChar)
                  00001b24    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001c44    00000118     Task_App.o (.text.Task_GraySensor)
                  00001d5c    00000110     OLED.o (.text.OLED_Init)
                  00001e6c    0000010c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001f78    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002084    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002188    000000f0     Motor.o (.text.Motor_SetDirc)
                  00002278    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000235c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002438    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002510    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000025c4    000000b4     Task.o (.text.Task_Add)
                  00002678    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00002722    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002724    000000a8     Motor.o (.text.Motor_SetSpeed)
                  000027cc    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000286e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002870    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002910    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000029a8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00002a34    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00002ab8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002b3c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002bbe    00000002     --HOLE-- [fill = 0]
                  00002bc0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002c3c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002cb0    00000070     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002d20    0000006e     OLED.o (.text.OLED_ShowString)
                  00002d8e    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002df8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002e60    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002ec6    00000002     --HOLE-- [fill = 0]
                  00002ec8    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002f2c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002f90    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002ff2    00000002     --HOLE-- [fill = 0]
                  00002ff4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003056    00000002     --HOLE-- [fill = 0]
                  00003058    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000030b8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003116    00000002     --HOLE-- [fill = 0]
                  00003118    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003174    0000005c     Task_App.o (.text.Task_Init)
                  000031d0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000322c    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003288    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000032e0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003338    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003390    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000033e6    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003438    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003488    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000034d8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00003524    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003570    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000035bc    0000004c     OLED.o (.text.OLED_Printf)
                  00003608    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003654    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000369e    00000002     --HOLE-- [fill = 0]
                  000036a0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000036ea    00000002     --HOLE-- [fill = 0]
                  000036ec    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003734    00000048     ADC.o (.text.adc_getValue)
                  0000377c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000037c0    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00003804    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00003846    00000002     --HOLE-- [fill = 0]
                  00003848    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000388a    00000002     --HOLE-- [fill = 0]
                  0000388c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000038cc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000390c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000394c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000398c    0000003e     Task.o (.text.Task_CMP)
                  000039ca    00000002     --HOLE-- [fill = 0]
                  000039cc    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003a08    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003a44    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003a80    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003abc    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003af8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003b34    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003b70    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003bac    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003be6    00000002     --HOLE-- [fill = 0]
                  00003be8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003c22    00000002     --HOLE-- [fill = 0]
                  00003c24    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003c58    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003c8c    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00003cbc    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00003cec    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003d1c    0000002c     Interrupt.o (.text.Interrupt_Init)
                  00003d48    0000002c     Motor.o (.text.Motor_Start)
                  00003d74    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003da0    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003dcc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003df8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003e24    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003e4c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003e74    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003e9c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003ec4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003eec    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00003f14    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003f3c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003f64    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003f8c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003fb2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003fd8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003ffc    00000024     Motor.o (.text.Motor_SetBothSpeed)
                  00004020    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004044    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004068    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000408a    00000002     --HOLE-- [fill = 0]
                  0000408c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000040ac    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000040cc    00000020     SysTick.o (.text.Delay)
                  000040ec    00000020     main.o (.text.main)
                  0000410c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000412a    00000002     --HOLE-- [fill = 0]
                  0000412c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000414a    00000002     --HOLE-- [fill = 0]
                  0000414c    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00004168    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00004184    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000041a0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000041bc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000041d8    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000041f4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004210    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000422c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004248    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004264    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004280    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000429c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000042b8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000042d4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000042f0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000430c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004328    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00004340    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00004358    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00004370    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004388    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000043a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000043b8    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000043d0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000043e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004400    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004418    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004430    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00004448    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00004460    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004478    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004490    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000044a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000044c0    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000044d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000044f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004508    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004520    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004538    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004550    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004568    00000018     OLED.o (.text.DL_I2C_reset)
                  00004580    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004598    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000045b0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000045c8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000045e0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000045f8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004610    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004628    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004640    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004658    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00004670    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00004688    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000046a0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000046b8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000046d0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000046e8    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004700    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00004716    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  0000472c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004742    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004758    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000476e    00000016     Task_App.o (.text.DL_GPIO_readPins)
                  00004784    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000479a    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000047b0    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000047c4    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000047d8    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000047ec    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00004800    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004814    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004828    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000483c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004850    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004864    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004878    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000488c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000048a0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000048b2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000048c4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000048d6    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000048e6    00000002     --HOLE-- [fill = 0]
                  000048e8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000048f8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004908    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004918    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004928    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00004936    00000002     --HOLE-- [fill = 0]
                  00004938    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004946    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004954    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004962    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000496e    00000002     --HOLE-- [fill = 0]
                  00004970    0000000c     SysTick.o (.text.Sys_GetTick)
                  0000497c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004986    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004990    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000049a0    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000049aa    0000000a            : vsprintf.c.obj (.text._outc)
                  000049b4    00000008     Interrupt.o (.text.SysTick_Handler)
                  000049bc    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000049c4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000049cc    00000006     libc.a : exit.c.obj (.text:abort)
                  000049d2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000049d6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000049da    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000049de    00000002     --HOLE-- [fill = 0]
                  000049e0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000049f0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000049f4    00000004     --HOLE-- [fill = 0]

.cinit     0    00005400    00000058     
                  00005400    0000002d     (.cinit..data.load) [load image, compression = lzss]
                  0000542d    00000003     --HOLE-- [fill = 0]
                  00005430    0000000c     (__TI_handler_table)
                  0000543c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005444    00000010     (__TI_cinit_table)
                  00005454    00000004     --HOLE-- [fill = 0]

.rodata    0    00004a00    00000a00     
                  00004a00    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004ff0    0000021c     OLED_Font.o (.rodata.asc2_0806)
                  0000520c    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000520f    00000001     --HOLE-- [fill = 0]
                  00005210    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005311    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005313    00000001     --HOLE-- [fill = 0]
                  00005314    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000533c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005354    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000536c    00000016     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00005382    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005393    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000053a4    0000000e     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000053b2    0000000b     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000053bd    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000053c8    0000000b     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000053d3    00000001     --HOLE-- [fill = 0]
                  000053d4    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000053de    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000053e0    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  000053e8    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  000053f0    00000005     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000053f5    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000053f7    00000009     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b5     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    000000b0     (.common:GraySensor)
                  202001a0    00000010     (.common:Gray_Anolog)
                  202001b0    00000004     (.common:ExISR_Flag)
                  202001b4    00000001     (.common:Gray_Digtal)

.data      0    202001b8    00000062     UNINITIALIZED
                  202001b8    00000020     Motor.o (.data.Motor_Left)
                  202001d8    00000020     Motor.o (.data.Motor_Right)
                  202001f8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202001fc    00000004     Task_App.o (.data.Task_GraySensor.debug_counter)
                  20200200    00000004     Task_App.o (.data.Task_OLED.display_counter)
                  20200204    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200208    00000004     SysTick.o (.data.delayTick)
                  2020020c    00000004     Task_App.o (.data.last_time)
                  20200210    00000004     SysTick.o (.data.uwTick)
                  20200214    00000002     Task_App.o (.data.last_encoder_left)
                  20200216    00000002     Task_App.o (.data.last_encoder_right)
                  20200218    00000001     Task_App.o (.data.Task_GraySensor.test_addr)
                  20200219    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3418    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3458    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1096    74        214    
       Interrupt.o                      378     0         4      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1474    74        218    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2060      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1066    0         0      
       Task.o                           674     0         241    
       Motor.o                          556     0         64     
       ADC.o                            236     0         0      
       SysTick.o                        84      0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4470    2060      313    
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1116    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5736    291       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       udivmoddi4.S.obj                 162     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2444    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       81        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     18702   2821      1047   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005444 records: 2, size/record: 8, table size: 16
	.data: load addr=00005400, load size=0000002d bytes, run addr=202001b8, run size=00000062 bytes, compression=lzss
	.bss: load addr=0000543c, load size=00000008 bytes, run addr=20200000, run size=000001b5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005430 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000014a5     00004990     0000498e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003f65     000049e0     000049da   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000049d3  ADC0_IRQHandler                      
000049d3  ADC1_IRQHandler                      
000049d3  AES_IRQHandler                       
000049d6  C$$EXIT                              
000049d3  CANFD0_IRQHandler                    
000049d3  DAC0_IRQHandler                      
0000388d  DL_ADC12_setClockConfig              
0000497d  DL_Common_delayCycles                
00003525  DL_DMA_initChannel                   
000030b9  DL_I2C_fillControllerTXFIFO          
00003fb3  DL_I2C_setClockConfig                
0000235d  DL_SYSCTL_configSYSPLL               
00002ec9  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000377d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002085  DL_Timer_initFourCCPWMMode           
000042d5  DL_Timer_setCaptCompUpdateMethod     
00004629  DL_Timer_setCaptureCompareOutCtl     
000048f9  DL_Timer_setCaptureCompareValue      
000042f1  DL_Timer_setClockConfig              
000036ed  DL_UART_init                         
000048a1  DL_UART_setClockConfig               
000049d3  DMA_IRQHandler                       
202001f8  Data_MotorEncoder                    
000049d3  Default_Handler                      
000040cd  Delay                                
202001b0  ExISR_Flag                           
000049d3  GROUP0_IRQHandler                    
00002511  GROUP1_IRQHandler                    
00001e6d  Get_Analog_value                     
00003a81  Get_Anolog_Value                     
00004929  Get_Digtal_For_User                  
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b4  Gray_Digtal                          
000049d7  HOSTexit                             
000049d3  HardFault_Handler                    
000049d3  I2C0_IRQHandler                      
000049d3  I2C1_IRQHandler                      
00002d8f  I2C_OLED_Clear                       
00003abd  I2C_OLED_Set_Pos                     
00002911  I2C_OLED_WR_Byte                     
00003059  I2C_OLED_i2c_sda_unlock              
00003d1d  Interrupt_Init                       
202001b8  Motor_Left                           
202001d8  Motor_Right                          
00003ffd  Motor_SetBothSpeed                   
00002725  Motor_SetSpeed                       
00003d49  Motor_Start                          
000049d3  NMI_Handler                          
00001639  No_MCU_Ganv_Sensor_Init_Frist        
00003805  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001d5d  OLED_Init                            
000035bd  OLED_Printf                          
000019f5  OLED_ShowChar                        
00002d21  OLED_ShowString                      
000049d3  PendSV_Handler                       
000049d3  RTC_IRQHandler                       
000049db  Reset_Handler                        
000049d3  SPI0_IRQHandler                      
000049d3  SPI1_IRQHandler                      
000049d3  SVC_Handler                          
00003609  SYSCFG_DL_ADC1_init                  
00003cbd  SYSCFG_DL_DMA_CH_RX_init             
000046d1  SYSCFG_DL_DMA_CH_TX_init             
00004963  SYSCFG_DL_DMA_init                   
0000111d  SYSCFG_DL_GPIO_init                  
00003289  SYSCFG_DL_I2C_MPU6050_init           
00002f2d  SYSCFG_DL_I2C_OLED_init              
000029a9  SYSCFG_DL_Motor_PWM_init             
00003119  SYSCFG_DL_SYSCTL_init                
00004909  SYSCFG_DL_SYSTICK_init               
00002a35  SYSCFG_DL_UART0_init                 
00003d75  SYSCFG_DL_init                       
00002871  SYSCFG_DL_initPower                  
000049b5  SysTick_Handler                      
00003f15  SysTick_Increasment                  
00004971  Sys_GetTick                          
000049d3  TIMA0_IRQHandler                     
000049d3  TIMA1_IRQHandler                     
000049d3  TIMG0_IRQHandler                     
000049d3  TIMG12_IRQHandler                    
000049d3  TIMG6_IRQHandler                     
000049d3  TIMG7_IRQHandler                     
000049d3  TIMG8_IRQHandler                     
000048b3  TI_memcpy_small                      
00004955  TI_memset_small                      
000025c5  Task_Add                             
00001c45  Task_GraySensor                      
00001637  Task_IdleFunction                    
00003175  Task_Init                            
00000a91  Task_OLED                            
000012f5  Task_Start                           
000049d3  UART0_IRQHandler                     
000049d3  UART1_IRQHandler                     
000049d3  UART2_IRQHandler                     
000049d3  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005444  __TI_CINIT_Base                      
00005454  __TI_CINIT_Limit                     
00005454  __TI_CINIT_Warm                      
00005430  __TI_Handler_Table_Base              
0000543c  __TI_Handler_Table_Limit             
00003b71  __TI_auto_init_nobinit_nopinit       
00002bc1  __TI_decompress_lzss                 
000048c5  __TI_decompress_none                 
000032e1  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000479b  __TI_zero_init_nomemset              
000014af  __adddf3                             
00005210  __aeabi_ctype_table_                 
00005210  __aeabi_ctype_table_C                
000036a1  __aeabi_d2iz                         
00003849  __aeabi_d2uiz                        
000014af  __aeabi_dadd                         
00002f91  __aeabi_dcmpeq                       
00002fcd  __aeabi_dcmpge                       
00002fe1  __aeabi_dcmpgt                       
00002fb9  __aeabi_dcmple                       
00002fa5  __aeabi_dcmplt                       
00001f79  __aeabi_ddiv                         
00002279  __aeabi_dmul                         
000014a5  __aeabi_dsub                         
20200204  __aeabi_errno                        
000049bd  __aeabi_errno_addr                   
0000390d  __aeabi_f2d                          
00002ff5  __aeabi_fcmpeq                       
00003031  __aeabi_fcmpge                       
00003045  __aeabi_fcmpgt                       
0000301d  __aeabi_fcmple                       
00003009  __aeabi_fcmplt                       
00002b3d  __aeabi_fdiv                         
00003dcd  __aeabi_i2d                          
00003af9  __aeabi_i2f                          
00003391  __aeabi_idiv                         
00002723  __aeabi_idiv0                        
00003391  __aeabi_idivmod                      
0000286f  __aeabi_ldiv0                        
0000412d  __aeabi_llsl                         
00004045  __aeabi_lmul                         
000049c5  __aeabi_memcpy                       
000049c5  __aeabi_memcpy4                      
000049c5  __aeabi_memcpy8                      
00004939  __aeabi_memset                       
00004939  __aeabi_memset4                      
00004939  __aeabi_memset8                      
00004021  __aeabi_ui2d                         
00003f3d  __aeabi_ui2f                         
000038cd  __aeabi_uidiv                        
000038cd  __aeabi_uidivmod                     
00004879  __aeabi_uldivmod                     
0000412d  __ashldi3                            
ffffffff  __binit__                            
00002df9  __cmpdf2                             
00003bad  __cmpsf2                             
00001f79  __divdf3                             
00002b3d  __divsf3                             
00002df9  __eqdf2                              
00003bad  __eqsf2                              
0000390d  __extendsfdf2                        
000036a1  __fixdfsi                            
00003849  __fixunsdfsi                         
00003dcd  __floatsidf                          
00003af9  __floatsisf                          
00004021  __floatunsidf                        
00003f3d  __floatunsisf                        
00002c3d  __gedf2                              
00003b35  __gesf2                              
00002c3d  __gtdf2                              
00003b35  __gtsf2                              
00002df9  __ledf2                              
00003bad  __lesf2                              
00002df9  __ltdf2                              
00003bad  __ltsf2                              
UNDEFED   __mpu_init                           
00002279  __muldf3                             
00004045  __muldi3                             
00003be9  __muldsi3                            
00002df9  __nedf2                              
00003bad  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000014a5  __subdf3                             
000027cd  __udivmoddi4                         
00003f65  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000049f1  _system_pre_init                     
000049cd  abort                                
00003735  adc_getValue                         
00004ff0  asc2_0806                            
00004a00  asc2_1608                            
0000394d  atoi                                 
ffffffff  binit                                
00002cb1  convertAnalogToDigital               
20200208  delayTick                            
000031d1  frexp                                
000031d1  frexpl                               
00000000  interruptVectors                     
00002439  ldexp                                
00002439  ldexpl                               
000040ed  main                                 
00004069  memccpy                              
00002679  normalizeAnalogValues                
000018c1  qsort                                
00002439  scalbn                               
00002439  scalbnl                              
20200210  uwTick                               
00003df9  vsprintf                             
00004919  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  Task_OLED                            
0000111d  SYSCFG_DL_GPIO_init                  
000012f5  Task_Start                           
000014a5  __aeabi_dsub                         
000014a5  __subdf3                             
000014af  __adddf3                             
000014af  __aeabi_dadd                         
00001637  Task_IdleFunction                    
00001639  No_MCU_Ganv_Sensor_Init_Frist        
000018c1  qsort                                
000019f5  OLED_ShowChar                        
00001c45  Task_GraySensor                      
00001d5d  OLED_Init                            
00001e6d  Get_Analog_value                     
00001f79  __aeabi_ddiv                         
00001f79  __divdf3                             
00002085  DL_Timer_initFourCCPWMMode           
00002279  __aeabi_dmul                         
00002279  __muldf3                             
0000235d  DL_SYSCTL_configSYSPLL               
00002439  ldexp                                
00002439  ldexpl                               
00002439  scalbn                               
00002439  scalbnl                              
00002511  GROUP1_IRQHandler                    
000025c5  Task_Add                             
00002679  normalizeAnalogValues                
00002723  __aeabi_idiv0                        
00002725  Motor_SetSpeed                       
000027cd  __udivmoddi4                         
0000286f  __aeabi_ldiv0                        
00002871  SYSCFG_DL_initPower                  
00002911  I2C_OLED_WR_Byte                     
000029a9  SYSCFG_DL_Motor_PWM_init             
00002a35  SYSCFG_DL_UART0_init                 
00002b3d  __aeabi_fdiv                         
00002b3d  __divsf3                             
00002bc1  __TI_decompress_lzss                 
00002c3d  __gedf2                              
00002c3d  __gtdf2                              
00002cb1  convertAnalogToDigital               
00002d21  OLED_ShowString                      
00002d8f  I2C_OLED_Clear                       
00002df9  __cmpdf2                             
00002df9  __eqdf2                              
00002df9  __ledf2                              
00002df9  __ltdf2                              
00002df9  __nedf2                              
00002ec9  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002f2d  SYSCFG_DL_I2C_OLED_init              
00002f91  __aeabi_dcmpeq                       
00002fa5  __aeabi_dcmplt                       
00002fb9  __aeabi_dcmple                       
00002fcd  __aeabi_dcmpge                       
00002fe1  __aeabi_dcmpgt                       
00002ff5  __aeabi_fcmpeq                       
00003009  __aeabi_fcmplt                       
0000301d  __aeabi_fcmple                       
00003031  __aeabi_fcmpge                       
00003045  __aeabi_fcmpgt                       
00003059  I2C_OLED_i2c_sda_unlock              
000030b9  DL_I2C_fillControllerTXFIFO          
00003119  SYSCFG_DL_SYSCTL_init                
00003175  Task_Init                            
000031d1  frexp                                
000031d1  frexpl                               
00003289  SYSCFG_DL_I2C_MPU6050_init           
000032e1  __TI_ltoa                            
00003391  __aeabi_idiv                         
00003391  __aeabi_idivmod                      
00003525  DL_DMA_initChannel                   
000035bd  OLED_Printf                          
00003609  SYSCFG_DL_ADC1_init                  
000036a1  __aeabi_d2iz                         
000036a1  __fixdfsi                            
000036ed  DL_UART_init                         
00003735  adc_getValue                         
0000377d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003805  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003849  __aeabi_d2uiz                        
00003849  __fixunsdfsi                         
0000388d  DL_ADC12_setClockConfig              
000038cd  __aeabi_uidiv                        
000038cd  __aeabi_uidivmod                     
0000390d  __aeabi_f2d                          
0000390d  __extendsfdf2                        
0000394d  atoi                                 
00003a81  Get_Anolog_Value                     
00003abd  I2C_OLED_Set_Pos                     
00003af9  __aeabi_i2f                          
00003af9  __floatsisf                          
00003b35  __gesf2                              
00003b35  __gtsf2                              
00003b71  __TI_auto_init_nobinit_nopinit       
00003bad  __cmpsf2                             
00003bad  __eqsf2                              
00003bad  __lesf2                              
00003bad  __ltsf2                              
00003bad  __nesf2                              
00003be9  __muldsi3                            
00003cbd  SYSCFG_DL_DMA_CH_RX_init             
00003d1d  Interrupt_Init                       
00003d49  Motor_Start                          
00003d75  SYSCFG_DL_init                       
00003dcd  __aeabi_i2d                          
00003dcd  __floatsidf                          
00003df9  vsprintf                             
00003f15  SysTick_Increasment                  
00003f3d  __aeabi_ui2f                         
00003f3d  __floatunsisf                        
00003f65  _c_int00_noargs                      
00003fb3  DL_I2C_setClockConfig                
00003ffd  Motor_SetBothSpeed                   
00004021  __aeabi_ui2d                         
00004021  __floatunsidf                        
00004045  __aeabi_lmul                         
00004045  __muldi3                             
00004069  memccpy                              
000040cd  Delay                                
000040ed  main                                 
0000412d  __aeabi_llsl                         
0000412d  __ashldi3                            
000042d5  DL_Timer_setCaptCompUpdateMethod     
000042f1  DL_Timer_setClockConfig              
00004629  DL_Timer_setCaptureCompareOutCtl     
000046d1  SYSCFG_DL_DMA_CH_TX_init             
0000479b  __TI_zero_init_nomemset              
00004879  __aeabi_uldivmod                     
000048a1  DL_UART_setClockConfig               
000048b3  TI_memcpy_small                      
000048c5  __TI_decompress_none                 
000048f9  DL_Timer_setCaptureCompareValue      
00004909  SYSCFG_DL_SYSTICK_init               
00004919  wcslen                               
00004929  Get_Digtal_For_User                  
00004939  __aeabi_memset                       
00004939  __aeabi_memset4                      
00004939  __aeabi_memset8                      
00004955  TI_memset_small                      
00004963  SYSCFG_DL_DMA_init                   
00004971  Sys_GetTick                          
0000497d  DL_Common_delayCycles                
000049b5  SysTick_Handler                      
000049bd  __aeabi_errno_addr                   
000049c5  __aeabi_memcpy                       
000049c5  __aeabi_memcpy4                      
000049c5  __aeabi_memcpy8                      
000049cd  abort                                
000049d3  ADC0_IRQHandler                      
000049d3  ADC1_IRQHandler                      
000049d3  AES_IRQHandler                       
000049d3  CANFD0_IRQHandler                    
000049d3  DAC0_IRQHandler                      
000049d3  DMA_IRQHandler                       
000049d3  Default_Handler                      
000049d3  GROUP0_IRQHandler                    
000049d3  HardFault_Handler                    
000049d3  I2C0_IRQHandler                      
000049d3  I2C1_IRQHandler                      
000049d3  NMI_Handler                          
000049d3  PendSV_Handler                       
000049d3  RTC_IRQHandler                       
000049d3  SPI0_IRQHandler                      
000049d3  SPI1_IRQHandler                      
000049d3  SVC_Handler                          
000049d3  TIMA0_IRQHandler                     
000049d3  TIMA1_IRQHandler                     
000049d3  TIMG0_IRQHandler                     
000049d3  TIMG12_IRQHandler                    
000049d3  TIMG6_IRQHandler                     
000049d3  TIMG7_IRQHandler                     
000049d3  TIMG8_IRQHandler                     
000049d3  UART0_IRQHandler                     
000049d3  UART1_IRQHandler                     
000049d3  UART2_IRQHandler                     
000049d3  UART3_IRQHandler                     
000049d6  C$$EXIT                              
000049d7  HOSTexit                             
000049db  Reset_Handler                        
000049f1  _system_pre_init                     
00004a00  asc2_1608                            
00004ff0  asc2_0806                            
00005210  __aeabi_ctype_table_                 
00005210  __aeabi_ctype_table_C                
00005430  __TI_Handler_Table_Base              
0000543c  __TI_Handler_Table_Limit             
00005444  __TI_CINIT_Base                      
00005454  __TI_CINIT_Limit                     
00005454  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b0  ExISR_Flag                           
202001b4  Gray_Digtal                          
202001b8  Motor_Left                           
202001d8  Motor_Right                          
202001f8  Data_MotorEncoder                    
20200204  __aeabi_errno                        
20200208  delayTick                            
20200210  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[225 symbols]

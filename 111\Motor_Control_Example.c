/**
 * @file Motor_Control_Example.c
 * @brief 纯手动电机控制使用示例
 * @version 2.0
 * @date 2025-08-01
 *
 * 这个文件展示了如何使用纯手动电机控制接口
 * 完全移除PID，直接控制电机的正反转和速度
 */

#include "Motor_Control.h"

/**
 * @brief 电机控制示例函数
 * 展示各种电机控制方法的使用
 */
void Motor_Control_Examples(void)
{
    // 基本运动控制
    
    // 停止电机
    MotorControl_Stop();
    
    // 前进，速度50%
    MotorControl_Forward(50.0f);
    
    // 后退，速度30%
    MotorControl_Backward(30.0f);
    
    // 左转，速度40%
    MotorControl_TurnLeft(40.0f);
    
    // 右转，速度40%
    MotorControl_TurnRight(40.0f);
    
    // 精确控制 - 分别设置左右电机速度
    
    // 直线前进（两个电机相同正速度）
    MotorControl_SetSpeed(60.0f, 60.0f);
    
    // 直线后退（两个电机相同负速度）
    MotorControl_SetSpeed(-50.0f, -50.0f);
    
    // 原地左转（左电机反转，右电机正转）
    MotorControl_SetSpeed(-40.0f, 40.0f);
    
    // 原地右转（左电机正转，右电机反转）
    MotorControl_SetSpeed(40.0f, -40.0f);
    
    // 大半径左转（左电机慢，右电机快）
    MotorControl_SetSpeed(20.0f, 70.0f);
    
    // 大半径右转（左电机快，右电机慢）
    MotorControl_SetSpeed(70.0f, 20.0f);
    
    // 获取当前速度设定值
    float left_speed = MotorControl_GetLeftSpeed();
    float right_speed = MotorControl_GetRightSpeed();
}

/**
 * @brief 简单的遥控示例
 * 模拟根据用户输入控制电机
 */
void Remote_Control_Example(void)
{
    // 假设这些是从遥控器或其他输入设备获取的值
    int command = 1;  // 1=前进, 2=后退, 3=左转, 4=右转, 0=停止
    float speed = 60.0f;  // 速度百分比
    
    switch(command)
    {
        case 0: // 停止
            MotorControl_Stop();
            break;
            
        case 1: // 前进
            MotorControl_Forward(speed);
            break;
            
        case 2: // 后退
            MotorControl_Backward(speed);
            break;
            
        case 3: // 左转
            MotorControl_TurnLeft(speed);
            break;
            
        case 4: // 右转
            MotorControl_TurnRight(speed);
            break;
            
        default:
            MotorControl_Stop();
            break;
    }
}

/**
 * @brief 自定义运动模式示例
 * 展示如何创建复杂的运动模式
 */
void Custom_Movement_Example(void)
{
    // 示例：S形运动
    
    // 阶段1：向右前方
    MotorControl_SetSpeed(80.0f, 40.0f);
    // 延时...
    
    // 阶段2：向左前方
    MotorControl_SetSpeed(40.0f, 80.0f);
    // 延时...
    
    // 阶段3：直线前进
    MotorControl_SetSpeed(60.0f, 60.0f);
    // 延时...
    
    // 停止
    MotorControl_Stop();
}

/**
 * @brief 速度渐变示例
 * 展示如何实现平滑的速度变化
 */
void Smooth_Speed_Example(void)
{
    // 从0逐渐加速到最大速度
    for(float speed = 0; speed <= 100; speed += 5)
    {
        MotorControl_Forward(speed);
        // 延时一小段时间...
    }
    
    // 从最大速度逐渐减速到0
    for(float speed = 100; speed >= 0; speed -= 5)
    {
        MotorControl_Forward(speed);
        // 延时一小段时间...
    }
    
    MotorControl_Stop();
}

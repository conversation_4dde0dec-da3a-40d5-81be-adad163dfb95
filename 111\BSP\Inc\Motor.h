#ifndef __Motor_h
#define __Motor_h

#include "SysConfig.h"

/**
 * @brief 电机方向
 *
 */
typedef enum
{
    DIRC_NONE = 0,
    DIRC_FOWARD, //正转
    DIRC_BACKWARD //反转
} Motor_DIRC_Def_t;



/**
 * @brief 电机编号
 * 
 */
typedef struct
{
    GPTIMER_Regs *Motor_PWM_TIMX; //PWM对应的定时器
    __IO int Motor_PWM_CH; //PWM通道
    __IO uint32_t Motor_Turn_Pin; //电机转动IO Pin
    int16_t *Motor_Encoder_Addr; //电机编码值地址（用于中断处理）
    Motor_DIRC_Def_t Motor_Dirc; //电机当前正转还是反转
    __IO uint32_t Motor_IN1_PIN;  // TB6612的IN1引脚
    __IO uint32_t Motor_IN2_PIN;  // TB6612的IN2引脚
    float Current_Speed;          // 当前设定速度值(-100~100)
} MOTOR_Def_t;

extern MOTOR_Def_t Motor_Left; //左轮
extern MOTOR_Def_t Motor_Right; //右轮

void Motor_Start(void);
bool Motor_SetSpeed(MOTOR_Def_t *Motor, float speed);
void Motor_SetBothSpeed(float left_speed, float right_speed);
void Motor_Stop(MOTOR_Def_t *Motor);
void Motor_StopAll(void);

#endif